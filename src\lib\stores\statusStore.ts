import { writable } from 'svelte/store';
import type { StatusMessage, StatusType } from '../types';

export const statusMessages = writable<StatusMessage[]>([]);

export function showStatus(text: string, type: StatusType = 'info', duration: number = 3000) {
    const id = Date.now();
    const newMessage: StatusMessage = { text, type, id };

    statusMessages.update(messages => [...messages, newMessage]);

    setTimeout(() => {
        statusMessages.update(messages => messages.filter(msg => msg.id !== id));
    }, duration);
}

export function clearStatus(id: number) {
    statusMessages.update(messages => messages.filter(msg => msg.id !== id));
}
