# API Keys Modal Unit Tests

This directory contains comprehensive unit tests for the API keys modal functionality using Vitest.

## Test Structure

### Test Files

1. **`ApiKeysModal.test.ts`** - Tests for API keys modal integration with the store
2. **`storageService.test.ts`** - Tests for localStorage storage service functionality
3. **`unifiedSettingsStore.test.ts`** - Tests for the unified settings store
4. **`apiKeysIntegration.test.ts`** - Integration tests demonstrating full workflow
5. **`setup.ts`** - Test setup and mocking configuration

### Key Features Tested

#### API Keys Modal Functionality
- ✅ API key updates for all providers (OpenAI, Gemini, Custom)
- ✅ Independent key management (updating one doesn't affect others)
- ✅ Empty string handling
- ✅ Multiple key updates
- ✅ Persistence simulation (refresh test)

#### Storage Service
- ✅ Loading and saving unified settings to localStorage
- ✅ API keys specific load/save functions
- ✅ Error handling for corrupted JSON
- ✅ Error handling for localStorage failures
- ✅ Partial settings merging with defaults
- ✅ Persistence across simulated page refreshes

#### Store Integration
- ✅ Store initialization with default values
- ✅ Store initialization from saved data
- ✅ Error handling during initialization
- ✅ API key updates through store functions

#### Integration Workflow
- ✅ Complete modal workflow: add keys → auto-save → retrieve after refresh
- ✅ Partial API key updates
- ✅ Empty keys handling
- ✅ Real localStorage persistence testing

## Running Tests

### Run All Tests
```bash
npm test
```

### Run Tests in Watch Mode
```bash
npm test -- --watch
```

### Run Specific Test Files
```bash
npm test ApiKeys        # Runs ApiKeysModal.test.ts and apiKeysIntegration.test.ts
npm test storage        # Runs storageService.test.ts
npm test unified        # Runs unifiedSettingsStore.test.ts
```

### Run Tests with UI
```bash
npm test:ui
```

### Run Tests Once (CI Mode)
```bash
npm test:run
```

## Test Configuration

### Vitest Configuration
- **Environment**: jsdom (for DOM APIs)
- **Setup Files**: `tests/setup.ts`
- **Path Aliases**: `@/` maps to `src/lib/`
- **Globals**: Enabled for describe, it, expect, etc.

### Mocking Strategy
- **localStorage**: Custom mock that behaves like real localStorage
- **External modules**: Mocked using vi.mock()
- **No network calls**: All external dependencies are mocked
- **Isolated tests**: Each test runs in isolation with fresh state

### Key Testing Principles
1. **No External Dependencies**: Tests don't make real network calls or access real files
2. **Isolated State**: Each test starts with clean localStorage and store state
3. **Comprehensive Coverage**: Tests cover happy paths, error cases, and edge cases
4. **Real Behavior Simulation**: Tests simulate actual user workflows
5. **Fast Execution**: All tests run quickly without external delays

## Test Coverage

The tests cover:
- ✅ API key storage and retrieval
- ✅ Automatic saving functionality
- ✅ Page refresh persistence
- ✅ Error handling and recovery
- ✅ Multiple provider support (OpenAI, Gemini, Custom)
- ✅ Empty and partial data handling
- ✅ Store state management
- ✅ localStorage integration

## Notes

- Tests use a custom localStorage mock that behaves like the real thing
- All external calls are mocked to prevent network requests
- Tests can be filtered using patterns (e.g., `npm test ApiKeys`)
- The `@/` path alias is configured for easy imports
- Error messages in stderr are expected for error handling tests
