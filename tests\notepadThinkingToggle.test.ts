import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/svelte';
import { get } from 'svelte/store';
import Notepad from '$lib/components/Notepad.svelte';
import {
    appSettings,
    updateAppSetting,
    initializeAllSettings,
    settingsLoaded
} from '$lib/stores/unifiedSettingsStore';
import { DEFAULT_APP_SETTINGS } from '$lib/config';

// Mock the API service
vi.mock('$lib/services/apiService', () => ({
    callAIWithThinking: vi.fn()
}));

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
    loadUnifiedSettings: vi.fn(),
    saveUnifiedSettings: vi.fn(),
    UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

// Mock status store
vi.mock('$lib/stores/statusStore', () => ({
    showStatus: vi.fn()
}));

describe('Notepad Thinking Panel Toggle Integration', () => {
    beforeEach(async () => {
        // Reset mocks
        vi.clearAllMocks();

        // Reset the store before each test
        appSettings.set({ ...DEFAULT_APP_SETTINGS, showThinkingPanel: true });
        settingsLoaded.set(true);

        await initializeAllSettings();
        await new Promise(resolve => setTimeout(resolve, 100));
    });

    it('should not show thinking toggle button when setting is disabled', async () => {
        // Disable thinking panel
        updateAppSetting('showThinkingPanel', false);
        
        const { container } = render(Notepad);
        
        // Look for thinking toggle button
        const thinkingButton = container.querySelector('.thinking-toggle');
        expect(thinkingButton).toBeNull();
    });

    it('should show thinking toggle button when setting is enabled and thinking content exists', async () => {
        // Enable thinking panel
        updateAppSetting('showThinkingPanel', true);
        
        const { container, component } = render(Notepad);
        
        // Simulate having thinking content by accessing the component's internal state
        // Note: This is a simplified test - in real usage, thinking content would come from AI responses
        const textarea = container.querySelector('textarea') as HTMLTextAreaElement;
        expect(textarea).toBeTruthy();
        
        // The thinking button should not be visible initially (no thinking content)
        let thinkingButton = container.querySelector('.thinking-toggle');
        expect(thinkingButton).toBeNull();
    });

    it('should not show thinking panel when setting is disabled', async () => {
        // Disable thinking panel
        updateAppSetting('showThinkingPanel', false);
        
        const { container } = render(Notepad);
        
        // Look for thinking panel
        const thinkingPanel = container.querySelector('.thinking-panel');
        expect(thinkingPanel).toBeNull();
    });

    it('should hide thinking panel when setting is toggled off', async () => {
        // Start with thinking panel enabled
        updateAppSetting('showThinkingPanel', true);
        
        const { container, component } = render(Notepad);
        
        // Initially no thinking panel should be visible (no thinking content)
        let thinkingPanel = container.querySelector('.thinking-panel');
        expect(thinkingPanel).toBeNull();
        
        // Disable thinking panel
        updateAppSetting('showThinkingPanel', false);
        
        // Wait for reactive update
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // Thinking panel should still be null (and would remain hidden even if content existed)
        thinkingPanel = container.querySelector('.thinking-panel');
        expect(thinkingPanel).toBeNull();
    });

    it('should respect thinking panel setting for text selection behavior', async () => {
        const { container } = render(Notepad);
        const textarea = container.querySelector('textarea') as HTMLTextAreaElement;
        
        // Test with setting enabled
        updateAppSetting('showThinkingPanel', true);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // Simulate text selection (though no thinking content exists yet)
        textarea.value = 'Some test content';
        textarea.setSelectionRange(0, 4);
        await fireEvent.select(textarea);
        
        // No thinking panel should appear (no thinking content)
        let thinkingPanel = container.querySelector('.thinking-panel');
        expect(thinkingPanel).toBeNull();
        
        // Test with setting disabled
        updateAppSetting('showThinkingPanel', false);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // Even if thinking content existed, panel should not appear
        textarea.setSelectionRange(5, 9);
        await fireEvent.select(textarea);
        
        thinkingPanel = container.querySelector('.thinking-panel');
        expect(thinkingPanel).toBeNull();
    });

    it('should maintain textarea functionality regardless of thinking panel setting', async () => {
        const { container } = render(Notepad);
        const textarea = container.querySelector('textarea') as HTMLTextAreaElement;
        
        // Test with thinking panel disabled
        updateAppSetting('showThinkingPanel', false);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // Textarea should still work normally
        await fireEvent.input(textarea, { target: { value: 'Test content' } });
        expect(textarea.value).toBe('Test content');
        
        // Test with thinking panel enabled
        updateAppSetting('showThinkingPanel', true);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // Textarea should still work normally
        await fireEvent.input(textarea, { target: { value: 'Updated content' } });
        expect(textarea.value).toBe('Updated content');
    });

    it('should show AI action buttons regardless of thinking panel setting', async () => {
        const { container } = render(Notepad);
        
        // Test with thinking panel disabled
        updateAppSetting('showThinkingPanel', false);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        const autocompleteButton = container.querySelector('button:has-text("Autocomplete")') || 
                                 Array.from(container.querySelectorAll('button')).find(btn => btn.textContent?.includes('Autocomplete'));
        expect(autocompleteButton).toBeTruthy();
        
        // Test with thinking panel enabled
        updateAppSetting('showThinkingPanel', true);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        const autocompleteButton2 = container.querySelector('button:has-text("Autocomplete")') || 
                                  Array.from(container.querySelectorAll('button')).find(btn => btn.textContent?.includes('Autocomplete'));
        expect(autocompleteButton2).toBeTruthy();
    });
});
