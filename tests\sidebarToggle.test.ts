import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { appSettings, updateAppSetting, initializeAllSettings } from '$lib/stores/unifiedSettingsStore';
import { DEFAULT_APP_SETTINGS } from '$lib/config';

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
    loadUnifiedSettings: vi.fn(),
    saveUnifiedSettings: vi.fn(),
    UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

describe('Sidebar Toggle Functionality', () => {
    beforeEach(async () => {
        // Reset the store before each test
        appSettings.set({ ...DEFAULT_APP_SETTINGS });
        await initializeAllSettings();
    });

    it('should have sidebarVisible property in default settings', () => {
        const settings = get(appSettings);
        expect(settings.sidebarVisible).toBeDefined();
        expect(settings.sidebarVisible).toBe(true);
    });

    it('should toggle sidebar visibility', () => {
        // Initially visible
        expect(get(appSettings).sidebarVisible).toBe(true);
        
        // Toggle to hidden
        updateAppSetting('sidebarVisible', false);
        expect(get(appSettings).sidebarVisible).toBe(false);
        
        // Toggle back to visible
        updateAppSetting('sidebarVisible', true);
        expect(get(appSettings).sidebarVisible).toBe(true);
    });

    it('should persist sidebar visibility state', () => {
        // Change the setting
        updateAppSetting('sidebarVisible', false);
        expect(get(appSettings).sidebarVisible).toBe(false);
        
        // The setting should remain after store operations
        const currentSettings = get(appSettings);
        expect(currentSettings.sidebarVisible).toBe(false);
    });
});
