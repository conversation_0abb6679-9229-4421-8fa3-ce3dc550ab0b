// Utilities for handling AI model thinking responses

/**
 * Represents the processed response from the AI, with thinking content separated.
 */
export interface ProcessedResponse {
    /** The main content of the response, without thinking tags. */
    content: string;
    /** The extracted thinking content, if any. */
    thinking: string | null;
    /** A flag indicating whether thinking content was found. */
    hasThinking: boolean;
}

/**
 * Processes a raw AI response to extract thinking content and clean the output.
 * @param rawResponse The raw response string from the AI.
 * @returns A `ProcessedResponse` object with the content and thinking separated.
 */
export function processThinkingResponse(rawResponse: string): ProcessedResponse {
    if (!rawResponse) {
        return { content: '', thinking: null, hasThinking: false };
    }

    // Look for thinking tags (case insensitive)
    const thinkingRegex = /<think>([\s\S]*?)<\/think>/gi;
    const matches = [...rawResponse.matchAll(thinkingRegex)];

    if (matches.length === 0) {
        return { content: rawResponse.trim(), thinking: null, hasThinking: false };
    }

    // Extract and combine thinking content
    const thinkingParts = matches
        .map(match => match[1].trim())
        .filter(part => part.length > 0);

    const thinking = thinkingParts.length > 0 ? thinkingParts.join('\n\n---\n\n') : null;
    const cleanContent = rawResponse.replace(thinkingRegex, '').trim();

    return {
        content: cleanContent,
        thinking,
        hasThinking: thinking !== null && thinking.length > 0
    };
}

/**
 * Formats the thinking content for display by cleaning up whitespace and paragraphs.
 * @param thinking The raw thinking string.
 * @returns The formatted thinking string.
 */
export function formatThinkingContent(thinking: string): string {
    if (!thinking) return '';

    return thinking
        .split('\n\n')
        .map(paragraph => paragraph.trim())
        .filter(paragraph => paragraph.length > 0)
        .join('\n\n');
}
