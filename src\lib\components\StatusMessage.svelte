<script lang="ts">
  import { statusMessages } from '../stores/statusStore';
</script>

<div class="status-messages-container">
  {#each $statusMessages as status (status.id)}
    <div class="status-message status-{status.type}">
      {status.text}
    </div>
  {/each}
</div>

<style>
  .status-messages-container {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
  }
  .status-message {
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.9em;
    box-shadow: 0 2px 10px var(--color-shadow);
    min-width: 200px;
    text-align: center;
    transition: all 0.2s ease;
  }
  .status-success {
    background-color: var(--color-success);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }
  .status-error {
    background-color: var(--color-error);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }
  .status-info {
    background-color: var(--color-info);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }
</style>
