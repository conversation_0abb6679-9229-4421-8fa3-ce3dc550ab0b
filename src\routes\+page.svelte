<script lang="ts">
  import Notepad from '$lib/components/Notepad.svelte';
  import Sidebar from '$lib/components/Sidebar.svelte';
  import SidebarToggle from '$lib/components/SidebarToggle.svelte';
  import { appSettings } from '$lib/stores/unifiedSettingsStore';
</script>

<main class="main-content">
  <Notepad />
  <div class="sidebar-container">
    <SidebarToggle />
    {#if $appSettings.sidebarVisible}
      <Sidebar />
    {/if}
  </div>
</main>

<style>
  .main-content {
    display: flex;
    flex-grow: 1;
    overflow: hidden;
  }

  .sidebar-container {
    position: relative;
    display: flex;
    flex-shrink: 0;
  }
</style>