import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { apiKeys, updateApiKey, initializeAllSettings } from '$lib/stores/unifiedSettingsStore';
import { loadApiKeys, saveApiKeys, loadUnifiedSettings } from '$lib/services/storageService';
import { DEFAULT_API_KEYS, DEFAULT_APP_SETTINGS, DEFAULT_LAST_SELECTED_MODELS } from '$lib/config';

// Mock the loadUnifiedSettings function
vi.mock('$lib/services/storageService', async () => {
	const actual = await vi.importActual('$lib/services/storageService');
	return {
		...actual,
		loadUnifiedSettings: vi.fn()
	};
});

describe('API Keys Integration Tests', () => {
	beforeEach(() => {
		// Clear localStorage before each test
		localStorage.clear();
		vi.clearAllMocks();

		// Reset store to default state
		apiKeys.set({ ...DEFAULT_API_KEYS });

		// Mock loadUnifiedSettings to return default values
		vi.mocked(loadUnifiedSettings).mockReturnValue({
			apiKeys: { ...DEFAULT_API_KEYS },
			appSettings: DEFAULT_APP_SETTINGS,
			notepadContent: '',
			customModelHistory: { gemini: [], openai: [], custom: [] },
			lastCustomModels: { gemini: '', openai: '', custom: '' },
			lastSelectedModels: DEFAULT_LAST_SELECTED_MODELS
		});
	});

	it('should save and load API keys through storage service', () => {
		const testApiKeys = {
			openai: 'sk-integration-test',
			gemini: 'gemini-integration-test',
			customUrl: 'https://api.integration.com',
			customKey: 'integration-custom-key'
		};

		// Save API keys using storage service
		saveApiKeys(testApiKeys);
		
		// Load API keys using storage service
		const loadedKeys = loadApiKeys();
		
		expect(loadedKeys).toEqual(testApiKeys);
	});

	it('should persist API keys across simulated page refresh', async () => {
		const testApiKeys = {
			openai: 'sk-refresh-test',
			gemini: 'gemini-refresh-test',
			customUrl: 'https://api.refresh.com',
			customKey: 'refresh-custom-key'
		};

		// Step 1: Set API keys using the store
		updateApiKey('openai', testApiKeys.openai);
		updateApiKey('gemini', testApiKeys.gemini);
		updateApiKey('customUrl', testApiKeys.customUrl);
		updateApiKey('customKey', testApiKeys.customKey);

		// Step 2: Save to storage (simulating auto-save)
		const currentKeys = get(apiKeys);
		saveApiKeys(currentKeys);

		// Step 3: Simulate page refresh by clearing store and reloading
		apiKeys.set({ ...DEFAULT_API_KEYS });

		// Verify store is reset
		expect(get(apiKeys)).toEqual(DEFAULT_API_KEYS);

		// Step 4: Mock loadUnifiedSettings to return the saved data
		vi.mocked(loadUnifiedSettings).mockReturnValue({
			apiKeys: testApiKeys,
			appSettings: DEFAULT_APP_SETTINGS,
			notepadContent: '',
			customModelHistory: { gemini: [], openai: [], custom: [] },
			lastCustomModels: { gemini: '', openai: '', custom: '' },
			lastSelectedModels: DEFAULT_LAST_SELECTED_MODELS
		});

		// Step 5: Initialize from storage (simulating page load)
		await initializeAllSettings();

		// Step 6: Verify keys are restored
		const restoredKeys = get(apiKeys);
		expect(restoredKeys).toEqual(testApiKeys);
	});

	it('should handle empty API keys correctly', () => {
		// Save empty keys
		saveApiKeys(DEFAULT_API_KEYS);
		
		// Load and verify
		const loadedKeys = loadApiKeys();
		expect(loadedKeys).toEqual(DEFAULT_API_KEYS);
		expect(loadedKeys.openai).toBe('');
		expect(loadedKeys.gemini).toBe('');
		expect(loadedKeys.customUrl).toBe('');
		expect(loadedKeys.customKey).toBe('');
	});

	it('should handle partial API key updates', () => {
		// Start with some keys set
		const initialKeys = {
			openai: 'sk-initial-key',
			gemini: 'initial-gemini-key',
			customUrl: '',
			customKey: ''
		};
		
		saveApiKeys(initialKeys);
		
		// Load and update only one key
		const loadedKeys = loadApiKeys();
		const updatedKeys = {
			...loadedKeys,
			customUrl: 'https://api.new.com'
		};
		
		saveApiKeys(updatedKeys);
		
		// Verify the update
		const finalKeys = loadApiKeys();
		expect(finalKeys).toEqual({
			openai: 'sk-initial-key',
			gemini: 'initial-gemini-key',
			customUrl: 'https://api.new.com',
			customKey: ''
		});
	});

	it('should demonstrate modal workflow: add keys, auto-save, and retrieve after refresh', async () => {
		// Simulate user opening modal and entering API keys
		const userEnteredKeys = {
			openai: 'sk-user-entered',
			gemini: 'user-gemini-key',
			customUrl: 'https://user.api.com',
			customKey: 'user-custom-key'
		};

		// User enters keys one by one (simulating typing in modal)
		updateApiKey('openai', userEnteredKeys.openai);
		expect(get(apiKeys).openai).toBe(userEnteredKeys.openai);

		updateApiKey('gemini', userEnteredKeys.gemini);
		expect(get(apiKeys).gemini).toBe(userEnteredKeys.gemini);

		updateApiKey('customUrl', userEnteredKeys.customUrl);
		expect(get(apiKeys).customUrl).toBe(userEnteredKeys.customUrl);

		updateApiKey('customKey', userEnteredKeys.customKey);
		expect(get(apiKeys).customKey).toBe(userEnteredKeys.customKey);

		// Simulate auto-save (would happen automatically in real app)
		const currentKeys = get(apiKeys);
		saveApiKeys(currentKeys);

		// Verify the keys were saved to localStorage
		const savedData = localStorage.getItem('aiNotepadSvelteUnifiedSettings');
		expect(savedData).toBeTruthy();
		const parsedData = JSON.parse(savedData!);
		expect(parsedData.apiKeys).toEqual(userEnteredKeys);

		// Simulate user closing modal and refreshing page
		apiKeys.set({ ...DEFAULT_API_KEYS });

		// Verify store is reset
		expect(get(apiKeys)).toEqual(DEFAULT_API_KEYS);

		// Now load the actual data from localStorage (simulating page refresh)
		const loadedData = loadApiKeys();
		expect(loadedData).toEqual(userEnteredKeys);
	});
});
