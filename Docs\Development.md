# Development Guide

## Overview

This guide covers development practices, code organization, and contribution guidelines for the AI Notepad application.

## Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager
- Modern browser for testing
- Code editor with TypeScript support

### Installation
```bash
# Clone repository
git clone <repository-url>
cd ai-notepad-1

# Install dependencies
npm install

# Start development server
npm run dev

# Open browser to http://localhost:5173
```

### Development Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run lint         # Run linter
npm run format       # Format code
```

## Code Organization

### Directory Structure
```
src/
├── lib/
│   ├── components/     # Reusable UI components
│   │   ├── Header.svelte
│   │   ├── Sidebar.svelte
│   │   ├── Notepad.svelte
│   │   ├── CustomModelSelect.svelte
│   │   └── ApiKeysModal.svelte
│   ├── stores/        # State management
│   │   ├── unifiedSettingsStore.ts
│   │   ├── statusStore.ts
│   │   └── customModelHistoryStore.ts
│   ├── services/      # Business logic
│   │   ├── apiService.ts
│   │   └── storageService.ts
│   ├── ai/           # AI provider adapters
│   │   ├── aiProvider.ts
│   │   ├── geminiAdapter.ts
│   │   ├── openAIAdapter.ts
│   │   ├── customAdapter.ts
│   │   └── aiUtils.ts
│   ├── utils/        # Helper functions
│   │   ├── textUtils.ts
│   │   ├── thinkingUtils.ts
│   │   └── modelUtils.ts
│   ├── types.ts      # TypeScript definitions
│   └── config.ts     # Application configuration
├── routes/           # SvelteKit routes
│   ├── +layout.svelte
│   └── +page.svelte
└── app.html         # HTML template
```

### Naming Conventions

#### Files and Directories
- **Components**: PascalCase (e.g., `CustomModelSelect.svelte`)
- **Services/Utils**: camelCase (e.g., `apiService.ts`)
- **Stores**: camelCase with "Store" suffix (e.g., `statusStore.ts`)
- **Types**: camelCase (e.g., `types.ts`)

#### Variables and Functions
- **Variables**: camelCase (e.g., `currentProvider`)
- **Functions**: camelCase (e.g., `handleProviderChange`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_TIMEOUT`)
- **Types/Interfaces**: PascalCase (e.g., `AppSettings`)

#### CSS Classes
- **BEM methodology**: block__element--modifier
- **Component-scoped**: Use Svelte's scoped styling
- **Utility classes**: kebab-case (e.g., `loading-placeholder`)

## Component Development

### Component Structure
```svelte
<script lang="ts">
  // Imports
  import { ... } from '...';
  
  // Props
  export let prop1: string;
  export let prop2: number = defaultValue;
  
  // State
  let localState: boolean = false;
  
  // Reactive statements
  $: derivedValue = prop1.toUpperCase();
  
  // Functions
  const handleEvent = () => {
    // Event handling logic
  };
  
  // Lifecycle
  onMount(() => {
    // Initialization logic
  });
</script>

<!-- Template with comments -->
<div class="component-wrapper">
  <!-- Component content -->
</div>

<style>
  /* Component-specific styles */
  .component-wrapper {
    /* Styles */
  }
</style>
```

### Component Guidelines

#### Single Responsibility
- Each component has one clear purpose
- Avoid mixing concerns within components
- Extract complex logic to services or utilities

#### Props and Events
- Use TypeScript for prop types
- Emit events for parent communication
- Keep prop interfaces minimal and focused

#### State Management
- Use stores for shared state
- Keep local state minimal
- Prefer reactive statements over imperative updates

#### Accessibility
- Include ARIA labels and roles
- Support keyboard navigation
- Provide screen reader support
- Test with accessibility tools

## Store Development

### Store Patterns

#### Writable Stores
```typescript
import { writable } from 'svelte/store';

export const myStore = writable<MyType>(defaultValue);

// Helper functions
export const updateMyStore = (newValue: MyType) => {
  myStore.set(newValue);
};
```

#### Derived Stores
```typescript
import { derived } from 'svelte/store';

export const derivedStore = derived(
  [store1, store2],
  ([$store1, $store2]) => {
    return computeValue($store1, $store2);
  }
);
```

#### Custom Stores
```typescript
function createCustomStore() {
  const { subscribe, set, update } = writable(initialValue);
  
  return {
    subscribe,
    customMethod: () => update(value => ({ ...value, modified: true })),
    reset: () => set(initialValue)
  };
}

export const customStore = createCustomStore();
```

### Store Guidelines

#### Persistence
- Use storage service for persistence
- Implement auto-save for user data
- Handle storage errors gracefully
- Provide migration for schema changes

#### Validation
- Validate data on load and update
- Provide sensible defaults
- Handle invalid data gracefully
- Log validation issues for debugging

#### Performance
- Minimize store subscriptions
- Use derived stores for computed values
- Batch related updates
- Avoid unnecessary reactivity

## Service Development

### Service Patterns

#### API Services
```typescript
export class ApiService {
  private baseUrl: string;
  
  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }
  
  async getData(): Promise<DataType> {
    try {
      const response = await fetch(`${this.baseUrl}/data`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }
}
```

#### Utility Services
```typescript
export const utilityService = {
  formatData: (data: RawData): FormattedData => {
    // Formatting logic
    return formattedData;
  },
  
  validateInput: (input: string): boolean => {
    // Validation logic
    return isValid;
  }
};
```

### Service Guidelines

#### Error Handling
- Use try-catch for async operations
- Provide meaningful error messages
- Log errors for debugging
- Handle different error types appropriately

#### Type Safety
- Use TypeScript interfaces for all data
- Validate external data at boundaries
- Provide type guards where needed
- Document complex types

#### Testing
- Write unit tests for all services
- Mock external dependencies
- Test error conditions
- Verify type safety

## AI Provider Development

### Adding New Providers

#### 1. Update Types
```typescript
// types.ts
type AIProviderType = 'gemini' | 'openai' | 'custom' | 'newProvider';
```

#### 2. Create Adapter
```typescript
// ai/newProviderAdapter.ts
import { AIProviderAdapter } from './aiProvider';

export class NewProviderAdapter extends AIProviderAdapter {
  async generate(prompt: string): Promise<string> {
    this.validateApiKey();
    
    const url = this.buildUrl();
    const body = this.buildRequestBody(prompt);
    const headers = this.buildHeaders();
    
    const data = await this.makeRequest(url, body, headers);
    return this.extractContent(data);
  }
  
  private buildUrl(): string {
    // Provider-specific URL construction
  }
  
  private buildRequestBody(prompt: string): any {
    // Provider-specific request format
  }
  
  private buildHeaders(): Record<string, string> {
    // Provider-specific headers
  }
  
  private extractContent(data: any): string {
    // Provider-specific response parsing
  }
}
```

#### 3. Update Configuration
```typescript
// config.ts
export const AVAILABLE_MODELS = {
  // ... existing providers
  newProvider: [
    { id: "model-1", name: "Model 1 - Description" },
    { id: "model-2", name: "Model 2 - Description" }
  ]
};

export const DEFAULT_LAST_SELECTED_MODELS = {
  // ... existing providers
  newProvider: AVAILABLE_MODELS.newProvider[0].id
};
```

#### 4. Update API Service
```typescript
// services/apiService.ts
function createAdapter(provider: AIProviderType, apiKeys: ApiKeys, model: string): AIProviderInterface {
  switch (provider) {
    // ... existing cases
    case 'newProvider':
      if (!apiKeys.newProviderKey) throw new Error("New Provider API Key is missing.");
      return new NewProviderAdapter(apiKeys.newProviderKey, model);
    // ...
  }
}
```

#### 5. Update UI
```typescript
// components/Sidebar.svelte
const aiProviders = [
  // ... existing providers
  { value: "newProvider", name: "New Provider" }
];
```

### Provider Guidelines

#### Request Handling
- Implement timeout mechanisms
- Handle rate limiting
- Retry on transient failures
- Validate request parameters

#### Response Processing
- Parse provider-specific formats
- Extract content consistently
- Handle error responses
- Validate response structure

#### Error Management
- Provide specific error messages
- Handle authentication errors
- Detect service unavailability
- Log errors for debugging

## Testing Guidelines

### Test Structure
```typescript
// tests/component.test.ts
import { render, fireEvent } from '@testing-library/svelte';
import Component from '../src/lib/components/Component.svelte';

describe('Component', () => {
  beforeEach(() => {
    // Setup before each test
  });
  
  afterEach(() => {
    // Cleanup after each test
  });
  
  test('should render correctly', () => {
    const { getByText } = render(Component, { props: { ... } });
    expect(getByText('Expected Text')).toBeInTheDocument();
  });
  
  test('should handle user interaction', async () => {
    const { getByRole } = render(Component);
    const button = getByRole('button');
    
    await fireEvent.click(button);
    
    // Assert expected behavior
  });
});
```

### Testing Best Practices

#### Unit Tests
- Test individual functions in isolation
- Mock external dependencies
- Cover edge cases and error conditions
- Aim for high code coverage

#### Integration Tests
- Test component interactions
- Verify store updates
- Test complete user workflows
- Mock external APIs

#### Mocking
- Mock external services consistently
- Use realistic mock data
- Test both success and failure scenarios
- Keep mocks simple and focused

## Code Quality

### TypeScript Guidelines

#### Type Definitions
- Use interfaces for object shapes
- Use type aliases for unions and primitives
- Prefer readonly for immutable data
- Use generics for reusable types

#### Type Safety
- Avoid `any` type
- Use type assertions sparingly
- Implement type guards for runtime checks
- Enable strict TypeScript settings

### Code Style

#### Formatting
- Use Prettier for consistent formatting
- Configure ESLint for code quality
- Follow established conventions
- Use meaningful variable names

#### Documentation
- Document complex functions
- Use JSDoc for public APIs
- Include usage examples
- Keep comments up to date

### Performance

#### Bundle Size
- Import only needed modules
- Use dynamic imports for large dependencies
- Optimize images and assets
- Monitor bundle size changes

#### Runtime Performance
- Minimize reactive statements
- Use efficient algorithms
- Avoid memory leaks
- Profile performance bottlenecks

## Deployment

### Build Process
```bash
# Production build
npm run build

# Preview build locally
npm run preview

# Deploy to hosting platform
# (Platform-specific commands)
```

### Environment Configuration
- Use environment variables for configuration
- Separate development and production settings
- Secure API keys and sensitive data
- Configure CORS for API access

### Monitoring
- Set up error tracking
- Monitor performance metrics
- Track user interactions
- Log important events

## Contributing

### Pull Request Process
1. Create feature branch from main
2. Implement changes with tests
3. Update documentation
4. Submit pull request
5. Address review feedback
6. Merge after approval

### Code Review Guidelines
- Review for functionality and correctness
- Check test coverage and quality
- Verify documentation updates
- Ensure consistent code style
- Test edge cases and error handling

### Issue Reporting
- Use issue templates
- Provide reproduction steps
- Include environment details
- Attach relevant logs or screenshots
- Label issues appropriately
