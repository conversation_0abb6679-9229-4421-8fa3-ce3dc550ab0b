import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import {
    appSettings,
    apiKeys,
    customModelHistory,
    providerSpecificModels,
    updateAppSetting,
    updateModelSelection,
    updateApiKey,
    switchAIProvider,
    addModelToHistory,
    initializeAllSettings,
    settingsLoaded
} from '$lib/stores/unifiedSettingsStore';
import { loadUnifiedSettings, saveUnifiedSettings, type UnifiedSettings } from '$lib/services/storageService';
import { DEFAULT_APP_SETTINGS, DEFAULT_API_KEYS, DEFAULT_PROVIDER_SPECIFIC_MODELS } from '$lib/config';
import { isCustomModel } from '$lib/utils/modelUtils';

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
    loadUnifiedSettings: vi.fn(),
    saveUnifiedSettings: vi.fn(),
    UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

describe('Sidebar Settings Persistence and Behavior', () => {
    let mockLocalStorage: Record<string, string>;

    beforeEach(async () => {
        mockLocalStorage = {};
        vi.clearAllMocks();

        Object.defineProperty(window, 'localStorage', {
            value: {
                getItem: vi.fn((key: string) => mockLocalStorage[key] || null),
                setItem: vi.fn((key: string, value: string) => {
                    mockLocalStorage[key] = value;
                }),
            },
            writable: true
        });

        appSettings.set({ ...DEFAULT_APP_SETTINGS });
        apiKeys.set({ ...DEFAULT_API_KEYS });
        customModelHistory.set({ gemini: [], openai: [], custom: [] });
        providerSpecificModels.set({ ...DEFAULT_PROVIDER_SPECIFIC_MODELS });

        vi.mocked(loadUnifiedSettings).mockReturnValue({
            appSettings: { ...DEFAULT_APP_SETTINGS },
            apiKeys: { ...DEFAULT_API_KEYS },
            notepadContent: '',
            customModelHistory: { gemini: [], openai: [], custom: [] },
            providerSpecificModels: { ...DEFAULT_PROVIDER_SPECIFIC_MODELS }
        });

        vi.mocked(saveUnifiedSettings).mockImplementation((settings: UnifiedSettings) => {
            mockLocalStorage['aiNotepadSvelteUnifiedSettings'] = JSON.stringify(settings);
            return true;
        });

        await initializeAllSettings();
        await new Promise(resolve => setTimeout(resolve, 100));
    });

    describe('Basic Settings Persistence', () => {
        it('should persist font family changes', async () => {
            updateAppSetting('fontFamily', "'Georgia', serif");
            await new Promise(resolve => setTimeout(resolve, 50));
            const storedData = JSON.parse(mockLocalStorage['aiNotepadSvelteUnifiedSettings']);
            vi.mocked(loadUnifiedSettings).mockReturnValue(storedData);
            await initializeAllSettings();
            expect(get(appSettings).fontFamily).toBe("'Georgia', serif");
        });
    });

    describe('AI Provider and Model Selection', () => {
        it('should persist model selections for each provider', async () => {
            switchAIProvider('gemini');
            updateModelSelection('gemini-1.5-pro');
            
            switchAIProvider('openai');
            updateModelSelection('gpt-4o');

            switchAIProvider('custom');
            updateModelSelection('my-custom-model');

            const models = get(providerSpecificModels);
            expect(models.gemini).toBe('gemini-1.5-pro');
            expect(models.openai).toBe('gpt-4o');
            expect(models.custom).toBe('my-custom-model');

            const storedData = JSON.parse(mockLocalStorage['aiNotepadSvelteUnifiedSettings']);
            vi.mocked(loadUnifiedSettings).mockReturnValue(storedData);
            await initializeAllSettings();

            const persistedModels = get(providerSpecificModels);
            expect(persistedModels.gemini).toBe('gemini-1.5-pro');
            expect(persistedModels.openai).toBe('gpt-4o');
            expect(persistedModels.custom).toBe('my-custom-model');
        });

        it('should restore the correct model when switching providers', () => {
            switchAIProvider('gemini');
            updateModelSelection('gemini-1.5-pro');
            
            switchAIProvider('openai');
            updateModelSelection('gpt-4o');

            switchAIProvider('gemini');
            expect(get(appSettings).aiModel).toBe('gemini-1.5-pro');

            switchAIProvider('openai');
            expect(get(appSettings).aiModel).toBe('gpt-4o');
        });
    });

    describe('Custom Model History', () => {
        it('should add valid custom models to history', () => {
            addModelToHistory('custom', 'custom-model-1');
            addModelToHistory('custom', 'custom-model-2');
            expect(get(customModelHistory).custom).toHaveLength(2);
            expect(get(customModelHistory).custom[0].model).toBe('custom-model-2');
        });

        it('should not add predefined models to custom history', () => {
            addModelToHistory('custom', 'gpt-4o-mini');
            expect(get(customModelHistory).custom).toHaveLength(0);
        });

        it('should not add empty or whitespace-only models to history', () => {
            addModelToHistory('custom', '');
            addModelToHistory('custom', '   ');
            expect(get(customModelHistory).custom).toHaveLength(0);
        });
    });

    describe('isCustomModel Utility', () => {
        it('should correctly identify custom and predefined models', () => {
            expect(isCustomModel('my-custom-model')).toBe(true);
            expect(isCustomModel('gpt-4o-mini')).toBe(false);
            expect(isCustomModel('gemini-1.5-pro')).toBe(false);
        });
    });
});
