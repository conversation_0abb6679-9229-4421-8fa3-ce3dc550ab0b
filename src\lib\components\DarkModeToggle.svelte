<script lang="ts">
  import { appSettings, updateAppSetting } from '../stores/unifiedSettingsStore';

  // Toggle dark mode setting
  const toggleDarkMode = () => {
    updateAppSetting('darkMode', !$appSettings.darkMode);
  };
</script>

<!-- Dark mode toggle button -->
<button
  class="dark-mode-toggle"
  on:click={toggleDarkMode}
  title={$appSettings.darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
  aria-label={$appSettings.darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
>
  <div class="toggle-icon">
    {#if $appSettings.darkMode}
      <!-- Sun icon for light mode -->
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2"/>
        <path d="M12 1v2m0 18v2M4.22 4.22l1.42 1.42m12.72 12.72l1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" stroke="currentColor" stroke-width="2"/>
      </svg>
    {:else}
      <!-- Moon icon for dark mode -->
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
      </svg>
    {/if}
  </div>
</button>

<style>
  .dark-mode-toggle {
    padding: 8px 12px;
    background-color: var(--color-button-secondary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-button-secondary-border);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    min-width: 40px;
    justify-content: center;
  }

  .dark-mode-toggle:hover {
    background-color: var(--color-toggle-hover);
    border-color: var(--color-accent);
  }

  .dark-mode-toggle:active {
    background-color: var(--color-toggle-active);
  }

  .dark-mode-toggle:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  .toggle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
  }

  .dark-mode-toggle:hover .toggle-icon {
    transform: scale(1.1);
  }

  .toggle-icon svg {
    transition: all 0.2s ease;
  }
</style>
