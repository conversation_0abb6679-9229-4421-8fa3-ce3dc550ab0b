# Types and Configuration Documentation

## Overview

The application uses TypeScript for type safety and a centralized configuration system. All types and configuration have been simplified for clarity.

## Core Types (`types.ts`)

### AI Provider Types

#### `AIProviderType`
```typescript
type AIProviderType = 'gemini' | 'openai' | 'custom';
```
Defines the supported AI providers.

#### `StatusType`
```typescript
type StatusType = 'info' | 'success' | 'error';
```
Defines status message types for user feedback.

### Configuration Interfaces

#### `ApiKeys`
```typescript
interface ApiKeys {
    gemini: string;      // Google Gemini API key
    openai: string;      // OpenAI API key
    customUrl: string;   // Custom provider base URL
    customKey: string;   // Custom provider API key
}
```
Stores all API credentials and endpoints.

#### `AppSettings`
```typescript
interface AppSettings {
    fontFamily: string;                // CSS font family
    fontSize: string;                  // Font size in pixels
    aiProvider: AIProviderType;        // Current AI provider
    aiModel: string;                   // Selected model ID
    autocompleteContextLength: number; // Context characters for AI
}
```
Main application configuration.

### AI System Interfaces

#### `AIProvider`
```typescript
interface AIProvider {
    generate(prompt: string): Promise<string>;
}
```
Common interface that all AI adapters must implement.

#### `StatusMessage`
```typescript
interface StatusMessage {
    text: string;      // Message content
    type: StatusType;  // Message type
    id: number;        // Unique identifier
}
```
Structure for user feedback messages.

### Model Management Types

#### `ModelHistoryItem`
```typescript
interface ModelHistoryItem {
    model: string;    // Model name/ID
    lastUsed: number; // Timestamp of last use
}
```
Tracks custom model usage history.

#### `ProviderSpecificModels`
```typescript
interface ProviderSpecificModels {
    gemini: string;
    openai: string;
    custom: string;
}
```
Preserves the last selected model for each provider.

#### `CustomModelHistory`
```typescript
interface CustomModelHistory {
    [provider: string]: ModelHistoryItem[];
}
```
Organizes model history by provider.

---

## Configuration (`config.ts`)

### Model Configuration

#### `ModelConfig`
```typescript
interface ModelConfig {
    id: string;    // Model identifier
    name: string;  // Human-readable description
}
```
Defines model metadata structure.

#### `AVAILABLE_MODELS`
```typescript
const AVAILABLE_MODELS: Record<string, ModelConfig[]> = {
    gemini: [
        { id: "gemini-2.5-flash-preview-04-17", name: "Gemini 2.5 Flash (Preview 04-17) - Very Fast, Experimental" },
        { id: "gemini-2.5-pro-preview-05-06", name: "Gemini 2.5 Pro (Preview 05-06) - Powerful, Experimental" },
        { id: "gemini-2.0-flash", name: "Gemini 2.0 Flash - Fast, Balanced" },
        { id: "gemini-2.0-flash-lite", name: "Gemini 2.0 Flash Lite - Very Fast, Lightweight" },
        { id: "gemini-1.5-flash", name: "Gemini 1.5 Flash - Fast, Efficient, Good Value" },
        { id: "gemini-1.5-flash-8b", name: "Gemini 1.5 Flash (8B) - Compact & Fast" },
        { id: "gemini-1.5-pro", name: "Gemini 1.5 Pro - Advanced, Large Context" }
    ],
    openai: [
        { id: "gpt-4o-mini", name: "GPT-4o mini - Fast, Cost-Effective, Multimodal" },
        { id: "gpt-4o", name: "GPT-4o - Flagship, Intelligent, Multimodal" },
        { id: "gpt-3.5-turbo", name: "GPT-3.5 Turbo - Fast, Affordable, Text-Focused" }
    ],
    custom: [] // Custom models are user-defined
};
```

### Application Constants

#### `AUTOCOMPLETE_CONTEXT_LENGTH`
- Default number of characters to use for AI context.

#### `API_TIMEOUT`
- Default timeout for API requests.

#### `OPENROUTER_HEADERS`
- Headers for OpenRouter API requests.

### Default Settings

#### `DEFAULT_APP_SETTINGS`
```typescript
const DEFAULT_APP_SETTINGS: AppSettings = {
    fontFamily: 'Arial, sans-serif',
    fontSize: '16',
    aiProvider: 'openai',
    aiModel: AVAILABLE_MODELS.openai[0].id,
    autocompleteContextLength: AUTOCOMPLETE_CONTEXT_LENGTH
};
```

#### `DEFAULT_API_KEYS`
```typescript
const DEFAULT_API_KEYS: ApiKeys = {
    gemini: '',
    openai: '',
    customUrl: '',
    customKey: ''
};
```

#### `DEFAULT_PROVIDER_SPECIFIC_MODELS`
- Default provider-specific models.

### Storage Configuration

#### `STORAGE_KEYS`
- Centralized localStorage key definitions for legacy migration.

---

## Type Safety Benefits

### Compile-Time Validation
- Catches type mismatches during development
- Ensures interface compliance
- Validates function signatures
- Prevents runtime type errors

### IDE Support
- Autocomplete for object properties
- Parameter hints for functions
- Refactoring safety
- Documentation integration

### Runtime Safety
- Clear contracts between components
- Predictable data structures
- Consistent error handling
- Reliable state management

---

## Configuration Management

### Centralized Constants
All configuration values are defined in one place:
- Easy to update and maintain
- Consistent across application
- Type-safe access
- Clear documentation

### Default Values
Comprehensive defaults ensure:
- Application works out of the box
- Graceful handling of missing data
- Consistent initial state
- Predictable behavior

### Model Configuration
Predefined models include:
- Unique identifiers
- Human-readable descriptions
- Performance characteristics
- Usage recommendations

### Storage Strategy
Organized storage keys:
- Prevent naming conflicts
- Enable easy migration
- Support backward compatibility
- Facilitate debugging

---

## Extension Points

### Adding New Providers
To add a new AI provider:
1. Add to `AIProviderType` union.
2. Update `AVAILABLE_MODELS` configuration.
3. Add a default model to `DEFAULT_PROVIDER_SPECIFIC_MODELS`.
4. Create an adapter that implements the `AIProvider` interface.
5. Update the `createAdapter` function in the API service to include the new provider.

### Adding New Models
To add models to existing providers:
1. Update `AVAILABLE_MODELS` configuration
2. Include model ID and description
3. Models automatically appear in UI

### Adding New Settings
To add application settings:
1. Update `AppSettings` interface
2. Add to `DEFAULT_APP_SETTINGS`
3. Update validation logic if needed
4. Add UI controls in Sidebar component

### Storage Migration
For storage format changes:
1. Update interfaces
2. Add migration logic in storage service
3. Maintain backward compatibility
4. Test with existing data
