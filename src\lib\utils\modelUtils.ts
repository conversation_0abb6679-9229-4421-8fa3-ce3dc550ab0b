import { AVAILABLE_MODELS } from '../config';

/**
 * Checks if a model is a custom model (i.e., not in the predefined lists).
 * @param model The model ID to check.
 * @returns `true` if the model is custom, `false` otherwise.
 */
export function isCustomModel(model: string): boolean {
    if (!model) return false;

    // Check if model exists in any predefined provider list
    return !Object.values(AVAILABLE_MODELS).some(provider =>
        provider.some(m => m.id === model)
    );
}

/**
 * Gets the description of a model for a specific provider.
 * @param provider The AI provider.
 * @param modelId The model ID.
 * @returns The model description, or `null` if not found.
 */
export function getModelDescription(provider: string, modelId: string): string | null {
    const models = AVAILABLE_MODELS[provider];
    return models?.find(m => m.id === modelId)?.name || null;
}

/**
 * Gets all available models for a specific provider.
 * @param provider The AI provider.
 * @returns An array of available models.
 */
export function getAvailableModels(provider: string) {
    return AVAILABLE_MODELS[provider] || [];
}
