# Architecture Overview

## System Design

The AI Notepad follows a clean, layered architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    UI Components Layer                      │
│  Header, <PERSON>bar, Notepad, CustomModelSelect, ApiKeysModal │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    State Management Layer                   │
│     Unified Settings Store, Status Store, Custom History   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer                           │
│        API Service, Storage Service, AI Adapters          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Utility Layer                           │
│      Text Utils, Thinking Utils, Model Utils              │
└─────────────────────────────────────────────────────────────┘
```

## Core Principles

### 1. Single Responsibility
Each component, service, and utility has a single, well-defined purpose:
- Components handle UI rendering and user interaction
- Stores manage application state
- Services handle business logic and external APIs
- Utilities provide pure helper functions

### 2. Reactive State Management
All application state is managed through Svelte stores with automatic reactivity:
- Settings changes trigger UI updates immediately
- Auto-save functionality prevents data loss
- State persistence across browser sessions

### 3. Provider Pattern for AI Services
AI providers implement a common interface, allowing easy switching between:
- OpenAI GPT models
- Google Gemini models  
- Custom OpenAI-compatible APIs

### 4. Error Boundary Pattern
Comprehensive error handling at each layer:
- User-friendly error messages
- Graceful degradation when APIs fail
- Validation at input boundaries

## Data Flow

### 1. User Interaction Flow
```
User Input → Component → Store Update → Auto-save → UI Refresh
```

### 2. AI Request Flow
```
User Action → Text Utils → API Service → AI Adapter → External API
     ↓
Status Updates ← Response Processing ← Thinking Extraction ← Raw Response
```

### 3. Settings Management Flow
```
UI Change → Store Update → Validation → Persistence → State Sync
```

## Key Design Decisions

### Unified Settings Store
- Single source of truth for all application settings
- Automatic persistence to localStorage
- Validation and migration support
- Provider-specific state isolation

### Base AI Adapter Pattern
- Common error handling and request logic
- Provider-specific implementations
- Consistent response format
- Timeout and retry mechanisms

### Component Composition
- Small, focused components
- Clear prop interfaces
- Event-driven communication
- Minimal component coupling

### Utility-First Approach
- Pure functions for text manipulation
- Reusable helper functions
- Clear input/output contracts
- Easy testing and debugging

## Performance Considerations

### 1. Lazy Loading
- Components load only when needed
- Settings initialize asynchronously
- API calls are debounced where appropriate

### 2. Memory Management
- Event listeners properly cleaned up
- Store subscriptions managed automatically
- Large text operations optimized

### 3. Storage Optimization
- Unified storage reduces localStorage calls
- Backward compatibility for migrations
- Efficient serialization/deserialization

## Security Considerations

### 1. API Key Management
- Keys stored only in localStorage
- No transmission to unauthorized endpoints
- Clear separation of provider credentials

### 2. Input Validation
- All user inputs validated before processing
- XSS prevention through proper escaping
- Safe HTML rendering practices

### 3. External API Safety
- Timeout mechanisms prevent hanging requests
- Error responses handled gracefully
- No sensitive data in error logs
