import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import {
    appSettings,
    updateAppSetting,
    initializeAllSettings,
    settingsLoaded
} from '$lib/stores/unifiedSettingsStore';
import { loadUnifiedSettings, saveUnifiedSettings, type UnifiedSettings } from '$lib/services/storageService';
import { DEFAULT_APP_SETTINGS } from '$lib/config';

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
    loadUnifiedSettings: vi.fn(),
    saveUnifiedSettings: vi.fn(),
    UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

describe('Thinking Panel Toggle Functionality', () => {
    let mockLocalStorage: Record<string, string> = {};

    beforeEach(async () => {
        // Reset mocks and localStorage
        vi.clearAllMocks();
        mockLocalStorage = {};

        // Reset the store before each test
        appSettings.set({ ...DEFAULT_APP_SETTINGS });

        vi.mocked(loadUnifiedSettings).mockReturnValue({
            appSettings: { ...DEFAULT_APP_SETTINGS },
            apiKeys: { gemini: '', openai: '', customUrl: '', customKey: '' },
            notepadContent: '',
            customModelHistory: { gemini: [], openai: [], custom: [] },
            providerSpecificModels: { gemini: 'gemini-2.5-flash-preview-04-17', openai: 'gpt-4o-mini', custom: '' }
        });

        vi.mocked(saveUnifiedSettings).mockImplementation((settings: UnifiedSettings) => {
            mockLocalStorage['aiNotepadSvelteUnifiedSettings'] = JSON.stringify(settings);
            return true;
        });

        await initializeAllSettings();
        await new Promise(resolve => setTimeout(resolve, 100));
    });

    it('should have showThinkingPanel property in default settings', () => {
        const settings = get(appSettings);
        expect(settings.showThinkingPanel).toBeDefined();
        expect(settings.showThinkingPanel).toBe(true);
    });

    it('should toggle thinking panel setting', () => {
        // Initially enabled
        expect(get(appSettings).showThinkingPanel).toBe(true);
        
        // Toggle to disabled
        updateAppSetting('showThinkingPanel', false);
        expect(get(appSettings).showThinkingPanel).toBe(false);
        
        // Toggle back to enabled
        updateAppSetting('showThinkingPanel', true);
        expect(get(appSettings).showThinkingPanel).toBe(true);
    });

    it('should persist thinking panel setting across reloads', async () => {
        // Change the setting
        updateAppSetting('showThinkingPanel', false);
        expect(get(appSettings).showThinkingPanel).toBe(false);

        // Wait for auto-save to complete
        await new Promise(resolve => setTimeout(resolve, 100));

        // Verify it was saved - get the last call to saveUnifiedSettings
        expect(vi.mocked(saveUnifiedSettings)).toHaveBeenCalled();
        const calls = vi.mocked(saveUnifiedSettings).mock.calls;
        const savedSettings = calls[calls.length - 1][0];
        expect(savedSettings.appSettings.showThinkingPanel).toBe(false);

        // Simulate reload by mocking loadUnifiedSettings to return the saved value
        vi.mocked(loadUnifiedSettings).mockReturnValue({
            appSettings: { ...DEFAULT_APP_SETTINGS, showThinkingPanel: false },
            apiKeys: { gemini: '', openai: '', customUrl: '', customKey: '' },
            notepadContent: '',
            customModelHistory: { gemini: [], openai: [], custom: [] },
            providerSpecificModels: { gemini: 'gemini-2.5-flash-preview-04-17', openai: 'gpt-4o-mini', custom: '' }
        });

        // Reinitialize settings
        await initializeAllSettings();
        await new Promise(resolve => setTimeout(resolve, 100));

        // Verify the setting was restored
        expect(get(appSettings).showThinkingPanel).toBe(false);
    });

    it('should work independently of other settings', () => {
        // Change thinking panel setting
        updateAppSetting('showThinkingPanel', false);
        
        // Change other settings
        updateAppSetting('fontSize', '18');
        updateAppSetting('fontFamily', 'Georgia, serif');
        updateAppSetting('sidebarVisible', false);
        
        // Thinking panel setting should remain unchanged
        expect(get(appSettings).showThinkingPanel).toBe(false);
        expect(get(appSettings).fontSize).toBe('18');
        expect(get(appSettings).fontFamily).toBe('Georgia, serif');
        expect(get(appSettings).sidebarVisible).toBe(false);
    });

    it('should maintain state consistency across multiple changes', () => {
        // Change state multiple times
        updateAppSetting('showThinkingPanel', false);
        updateAppSetting('showThinkingPanel', true);
        updateAppSetting('showThinkingPanel', false);
        updateAppSetting('showThinkingPanel', true);
        updateAppSetting('showThinkingPanel', false);
        
        expect(get(appSettings).showThinkingPanel).toBe(false);
    });

    it('should handle boolean type correctly', () => {
        // Test with explicit boolean values
        updateAppSetting('showThinkingPanel', true);
        expect(get(appSettings).showThinkingPanel).toBe(true);
        expect(typeof get(appSettings).showThinkingPanel).toBe('boolean');
        
        updateAppSetting('showThinkingPanel', false);
        expect(get(appSettings).showThinkingPanel).toBe(false);
        expect(typeof get(appSettings).showThinkingPanel).toBe('boolean');
    });

    it('should auto-save when thinking panel setting changes', () => {
        // Clear previous calls
        vi.clearAllMocks();
        
        // Change the setting
        updateAppSetting('showThinkingPanel', false);
        
        // Verify auto-save was triggered
        expect(vi.mocked(saveUnifiedSettings)).toHaveBeenCalled();
        
        // Verify the correct value was saved
        const savedSettings = vi.mocked(saveUnifiedSettings).mock.calls[0][0];
        expect(savedSettings.appSettings.showThinkingPanel).toBe(false);
    });
});
