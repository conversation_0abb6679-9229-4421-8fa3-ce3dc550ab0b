import { browser } from '$app/environment';
import type { ApiKeys, AppSettings, CustomModelHistory, ProviderSpecificModels } from '../types';
import {
    DEFAULT_API_KEYS,
    DEFAULT_APP_SETTINGS,
    DEFAULT_PROVIDER_SPECIFIC_MODELS,
    STORAGE_KEYS
} from '../config';

// Unified settings interface for all persistent data
export interface UnifiedSettings {
    appSettings: AppSettings;
    apiKeys: ApiKeys;
    notepadContent: string;
    customModelHistory: CustomModelHistory;
    providerSpecificModels: ProviderSpecificModels;
}

// Storage key for unified settings
export const UNIFIED_SETTINGS_STORAGE_KEY = 'aiNotepadSvelteUnifiedSettings';

// Safely parse JSON with fallback
function safeJsonParse<T>(jsonString: string | null, fallback: T): T {
    if (!jsonString) return fallback;
    try {
        return JSON.parse(jsonString);
    } catch {
        return fallback;
    }
}

// Create default settings structure
function createDefaultSettings(): UnifiedSettings {
    return {
        appSettings: { ...DEFAULT_APP_SETTINGS },
        apiKeys: { ...DEFAULT_API_KEYS },
        notepadContent: "",
        customModelHistory: { gemini: [], openai: [], custom: [] },
        providerSpecificModels: { ...DEFAULT_PROVIDER_SPECIFIC_MODELS }
    };
}

// Load unified settings from storage, with fallback to legacy keys for migration
export function loadUnifiedSettings(): UnifiedSettings {
    if (!browser) return createDefaultSettings();

    const unifiedStored = localStorage.getItem(UNIFIED_SETTINGS_STORAGE_KEY);
    if (unifiedStored) {
        const parsed = safeJsonParse(unifiedStored, {}) as Partial<UnifiedSettings>;
        // Deep merge with defaults to ensure all keys are present
        return {
            appSettings: { ...DEFAULT_APP_SETTINGS, ...parsed.appSettings },
            apiKeys: { ...DEFAULT_API_KEYS, ...parsed.apiKeys },
            notepadContent: parsed.notepadContent || "",
            customModelHistory: parsed.customModelHistory || { gemini: [], openai: [], custom: [] },
            providerSpecificModels: { ...DEFAULT_PROVIDER_SPECIFIC_MODELS, ...parsed.providerSpecificModels }
        };
    }

    // Legacy migration: if unified settings don't exist, try loading from old keys
    const legacySettings: UnifiedSettings = {
        appSettings: safeJsonParse(localStorage.getItem(STORAGE_KEYS.APP_SETTINGS), DEFAULT_APP_SETTINGS),
        apiKeys: safeJsonParse(localStorage.getItem(STORAGE_KEYS.API_KEYS), DEFAULT_API_KEYS),
        notepadContent: localStorage.getItem(STORAGE_KEYS.NOTEPAD_CONTENT) || "",
        customModelHistory: safeJsonParse(localStorage.getItem(STORAGE_KEYS.CUSTOM_MODEL_HISTORY), { gemini: [], openai: [], custom: [] }),
        providerSpecificModels: safeJsonParse(localStorage.getItem(STORAGE_KEYS.PROVIDER_SPECIFIC_MODELS), DEFAULT_PROVIDER_SPECIFIC_MODELS)
    };

    // If we loaded any legacy settings, save them to the new unified key
    if (Object.values(STORAGE_KEYS).some(key => localStorage.getItem(key) !== null)) {
        saveUnifiedSettings(legacySettings);
        // Clean up old keys after migration
        Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
    }

    return legacySettings;
}

// Save unified settings to storage
export function saveUnifiedSettings(settings: UnifiedSettings): boolean {
    if (!browser) return false;
    try {
        localStorage.setItem(UNIFIED_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
        return true;
    } catch (error) {
        console.error(`Failed to save to ${UNIFIED_SETTINGS_STORAGE_KEY}:`, error);
        return false;
    }
}
