<script lang="ts">
  import { appSettings, updateAppSetting } from '../stores/unifiedSettingsStore';

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    updateAppSetting('sidebarVisible', !$appSettings.sidebarVisible);
  };
</script>

<!-- Sidebar toggle button -->
<button
  class="sidebar-toggle"
  on:click={toggleSidebar}
  title={$appSettings.sidebarVisible ? 'Hide sidebar' : 'Show sidebar'}
  aria-label={$appSettings.sidebarVisible ? 'Hide sidebar' : 'Show sidebar'}
>
  <div class="arrow" class:arrow-right={$appSettings.sidebarVisible} class:arrow-left={!$appSettings.sidebarVisible}></div>
</button>

<style>
  .sidebar-toggle {
    position: absolute;
    top: 30px;
    left: -35px;
    width: 35px;
    height: 40px;
    background-color: var(--color-toggle-bg);
    color: var(--color-toggle-arrow);
    border: none;
    border-radius: 20px 0 0 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    box-shadow: 0 2px 4px var(--color-shadow);
    padding: 0 0 0 4px;
  }

  .sidebar-toggle:hover {
    background-color: var(--color-toggle-hover);
  }

  .sidebar-toggle:active {
    background-color: var(--color-toggle-active);
  }

  .arrow {
    width: 0;
    height: 0;
    transition: all 0.2s ease;
  }

  .arrow-right {
    border-left: 6px solid var(--color-toggle-arrow);
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
  }

  .arrow-left {
    border-right: 6px solid var(--color-toggle-arrow);
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
  }
</style>
