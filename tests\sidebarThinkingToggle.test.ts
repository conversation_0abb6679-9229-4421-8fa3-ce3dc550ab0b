import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/svelte';
import { get } from 'svelte/store';
import Sidebar from '$lib/components/Sidebar.svelte';
import {
    appSettings,
    updateAppSetting,
    initializeAllSettings,
    settingsLoaded
} from '$lib/stores/unifiedSettingsStore';
import { DEFAULT_APP_SETTINGS } from '$lib/config';

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
    loadUnifiedSettings: vi.fn(),
    saveUnifiedSettings: vi.fn(),
    UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

describe('Sidebar Thinking Panel Toggle', () => {
    beforeEach(async () => {
        // Reset mocks
        vi.clearAllMocks();

        // Reset the store before each test
        appSettings.set({ ...DEFAULT_APP_SETTINGS, showThinkingPanel: true });
        settingsLoaded.set(true);

        await initializeAllSettings();
        await new Promise(resolve => setTimeout(resolve, 100));
    });

    it('should render thinking panel toggle checkbox', async () => {
        const { container } = render(Sidebar);
        
        const checkbox = container.querySelector('#showThinkingPanel') as HTMLInputElement;
        expect(checkbox).toBeTruthy();
        expect(checkbox.type).toBe('checkbox');
    });

    it('should show correct initial state of thinking panel toggle', async () => {
        const { container } = render(Sidebar);
        
        const checkbox = container.querySelector('#showThinkingPanel') as HTMLInputElement;
        expect(checkbox.checked).toBe(true);
    });

    it('should update store when thinking panel toggle is clicked', async () => {
        const { container } = render(Sidebar);
        
        const checkbox = container.querySelector('#showThinkingPanel') as HTMLInputElement;
        expect(checkbox.checked).toBe(true);
        
        // Click the checkbox to disable thinking panel
        await fireEvent.click(checkbox);
        
        // Verify the store was updated
        expect(get(appSettings).showThinkingPanel).toBe(false);
        expect(checkbox.checked).toBe(false);
    });

    it('should reflect store changes in the UI', async () => {
        const { container } = render(Sidebar);
        
        const checkbox = container.querySelector('#showThinkingPanel') as HTMLInputElement;
        expect(checkbox.checked).toBe(true);
        
        // Update the store directly
        updateAppSetting('showThinkingPanel', false);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // Verify the checkbox reflects the change
        expect(checkbox.checked).toBe(false);
        
        // Update back to true
        updateAppSetting('showThinkingPanel', true);
        await new Promise(resolve => setTimeout(resolve, 0));
        
        expect(checkbox.checked).toBe(true);
    });

    it('should have proper label text', async () => {
        const { container } = render(Sidebar);
        
        const label = container.querySelector('label[class*="checkbox-label"]');
        expect(label).toBeTruthy();
        expect(label?.textContent).toContain('Show AI Thinking Process');
    });

    it('should have descriptive help text', async () => {
        const { container } = render(Sidebar);
        
        const description = Array.from(container.querySelectorAll('.setting-description'))
            .find(el => el.textContent?.includes('thinking process'));
        expect(description).toBeTruthy();
        expect(description?.textContent).toContain('Display the AI\'s thinking process when available');
    });

    it('should work independently of other settings', async () => {
        const { container } = render(Sidebar);
        
        const thinkingCheckbox = container.querySelector('#showThinkingPanel') as HTMLInputElement;
        const fontSizeInput = container.querySelector('#fontSize') as HTMLInputElement;
        
        // Change thinking panel setting
        await fireEvent.click(thinkingCheckbox);
        expect(get(appSettings).showThinkingPanel).toBe(false);
        
        // Change font size
        await fireEvent.input(fontSizeInput, { target: { value: '18' } });
        expect(get(appSettings).fontSize).toBe('18');
        
        // Thinking panel setting should remain unchanged
        expect(get(appSettings).showThinkingPanel).toBe(false);
        expect(thinkingCheckbox.checked).toBe(false);
    });

    it('should be positioned correctly in the settings layout', async () => {
        const { container } = render(Sidebar);
        
        // Find the thinking panel setting group
        const thinkingSettingsGroup = container.querySelector('#showThinkingPanel')?.closest('.settings-group');
        expect(thinkingSettingsGroup).toBeTruthy();
        
        // Verify it's positioned after font settings but before AI configuration
        const allSettingsGroups = Array.from(container.querySelectorAll('.settings-group'));
        const thinkingGroupIndex = allSettingsGroups.indexOf(thinkingSettingsGroup!);
        
        // Should not be the first group (font settings come first)
        expect(thinkingGroupIndex).toBeGreaterThan(0);
        
        // Should come before AI configuration section
        const aiConfigHeader = Array.from(container.querySelectorAll('h3'))
            .find(h => h.textContent?.includes('AI Configuration'));
        expect(aiConfigHeader).toBeTruthy();
    });

    it('should handle rapid toggle changes correctly', async () => {
        const { container } = render(Sidebar);
        
        const checkbox = container.querySelector('#showThinkingPanel') as HTMLInputElement;
        
        // Rapidly toggle the checkbox multiple times
        await fireEvent.click(checkbox);
        expect(get(appSettings).showThinkingPanel).toBe(false);
        
        await fireEvent.click(checkbox);
        expect(get(appSettings).showThinkingPanel).toBe(true);
        
        await fireEvent.click(checkbox);
        expect(get(appSettings).showThinkingPanel).toBe(false);
        
        await fireEvent.click(checkbox);
        expect(get(appSettings).showThinkingPanel).toBe(true);
        
        // Final state should be consistent
        expect(checkbox.checked).toBe(true);
        expect(get(appSettings).showThinkingPanel).toBe(true);
    });
});
