import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
	loadUnifiedSettings,
	saveUnifiedSettings,
	loadApiKeys,
	saveApiKeys,
	UNIFIED_SETTINGS_STORAGE_KEY
} from '$lib/services/storageService';
import { DEFAULT_API_KEYS, DEFAULT_APP_SETTINGS, DEFAULT_LAST_SELECTED_MODELS } from '$lib/config';

describe('Storage Service', () => {
	beforeEach(() => {
		// Clear localStorage before each test
		localStorage.clear();
		vi.clearAllMocks();
	});

	describe('loadUnifiedSettings', () => {
		it('should return default settings when localStorage is empty', () => {
			const settings = loadUnifiedSettings();
			
			expect(settings.apiKeys).toEqual(DEFAULT_API_KEYS);
			expect(settings.appSettings).toEqual(DEFAULT_APP_SETTINGS);
			expect(settings.notepadContent).toBe('');
			expect(settings.customModelHistory).toEqual({ gemini: [], openai: [], custom: [] });
			expect(settings.lastCustomModels).toEqual({ gemini: '', openai: '', custom: '' });
			expect(settings.lastSelectedModels).toEqual(DEFAULT_LAST_SELECTED_MODELS);
		});

		it('should load settings from unified storage key', () => {
			const testSettings = {
				apiKeys: {
					openai: 'sk-test-openai',
					gemini: 'test-gemini-key',
					customUrl: 'https://api.test.com',
					customKey: 'test-custom-key'
				},
				appSettings: {
					...DEFAULT_APP_SETTINGS,
					fontSize: '18'
				},
				notepadContent: 'Test content',
				customModelHistory: { gemini: [], openai: [], custom: [] },
				lastCustomModels: { gemini: '', openai: '', custom: '' },
				lastSelectedModels: DEFAULT_LAST_SELECTED_MODELS
			};

			localStorage.setItem(UNIFIED_SETTINGS_STORAGE_KEY, JSON.stringify(testSettings));
			
			const loaded = loadUnifiedSettings();
			expect(loaded).toEqual(testSettings);
		});

		it('should handle corrupted JSON gracefully', () => {
			localStorage.setItem(UNIFIED_SETTINGS_STORAGE_KEY, 'invalid-json');
			
			const settings = loadUnifiedSettings();
			expect(settings.apiKeys).toEqual(DEFAULT_API_KEYS);
		});

		it('should merge partial settings with defaults', () => {
			const partialSettings = {
				apiKeys: {
					openai: 'sk-test-key'
					// Missing other keys
				},
				appSettings: {
					fontSize: '20'
					// Missing other settings
				}
			};

			localStorage.setItem(UNIFIED_SETTINGS_STORAGE_KEY, JSON.stringify(partialSettings));
			
			const loaded = loadUnifiedSettings();
			expect(loaded.apiKeys.openai).toBe('sk-test-key');
			expect(loaded.apiKeys.gemini).toBe(''); // Should use default
			expect(loaded.appSettings.fontSize).toBe('20');
			expect(loaded.appSettings.fontFamily).toBe(DEFAULT_APP_SETTINGS.fontFamily); // Should use default
		});
	});

	describe('saveUnifiedSettings', () => {
		it('should save settings to localStorage', () => {
			const testSettings = {
				apiKeys: {
					openai: 'sk-test-openai',
					gemini: 'test-gemini-key',
					customUrl: 'https://api.test.com',
					customKey: 'test-custom-key'
				},
				appSettings: DEFAULT_APP_SETTINGS,
				notepadContent: 'Test content',
				customModelHistory: { gemini: [], openai: [], custom: [] },
				lastCustomModels: { gemini: '', openai: '', custom: '' },
				lastSelectedModels: DEFAULT_LAST_SELECTED_MODELS
			};

			const success = saveUnifiedSettings(testSettings);

			expect(success).toBe(true);

			// Verify the data was actually saved to localStorage
			const saved = localStorage.getItem(UNIFIED_SETTINGS_STORAGE_KEY);
			expect(saved).toBe(JSON.stringify(testSettings));
		});

		it('should handle localStorage errors gracefully', () => {
			// Temporarily replace localStorage.setItem to simulate an error
			const originalSetItem = localStorage.setItem;
			localStorage.setItem = vi.fn(() => {
				throw new Error('Storage quota exceeded');
			});

			const testSettings = {
				apiKeys: DEFAULT_API_KEYS,
				appSettings: DEFAULT_APP_SETTINGS,
				notepadContent: '',
				customModelHistory: { gemini: [], openai: [], custom: [] },
				lastCustomModels: { gemini: '', openai: '', custom: '' },
				lastSelectedModels: DEFAULT_LAST_SELECTED_MODELS
			};

			const success = saveUnifiedSettings(testSettings);
			expect(success).toBe(false);

			// Restore original setItem
			localStorage.setItem = originalSetItem;
		});
	});

	describe('API Keys specific functions', () => {
		it('should load API keys using loadApiKeys', () => {
			const testApiKeys = {
				openai: 'sk-test-openai',
				gemini: 'test-gemini-key',
				customUrl: 'https://api.test.com',
				customKey: 'test-custom-key'
			};

			const testSettings = {
				apiKeys: testApiKeys,
				appSettings: DEFAULT_APP_SETTINGS,
				notepadContent: '',
				customModelHistory: { gemini: [], openai: [], custom: [] },
				lastCustomModels: { gemini: '', openai: '', custom: '' },
				lastSelectedModels: DEFAULT_LAST_SELECTED_MODELS
			};

			localStorage.setItem(UNIFIED_SETTINGS_STORAGE_KEY, JSON.stringify(testSettings));
			
			const loaded = loadApiKeys();
			expect(loaded).toEqual(testApiKeys);
		});

		it('should save API keys using saveApiKeys', () => {
			const testApiKeys = {
				openai: 'sk-new-openai',
				gemini: 'new-gemini-key',
				customUrl: 'https://api.new.com',
				customKey: 'new-custom-key'
			};

			saveApiKeys(testApiKeys);
			
			// Verify that the unified settings were updated
			const saved = JSON.parse(localStorage.getItem(UNIFIED_SETTINGS_STORAGE_KEY) || '{}');
			expect(saved.apiKeys).toEqual(testApiKeys);
		});
	});

	describe('Persistence simulation (refresh test)', () => {
		it('should persist API keys across simulated page refresh', () => {
			// Simulate setting API keys
			const originalApiKeys = {
				openai: 'sk-persistent-key',
				gemini: 'persistent-gemini-key',
				customUrl: 'https://persistent.api.com',
				customKey: 'persistent-custom-key'
			};

			saveApiKeys(originalApiKeys);
			
			// Simulate page refresh by clearing in-memory state and reloading
			// (In a real app, this would be handled by the store initialization)
			const loadedSettings = loadUnifiedSettings();
			
			expect(loadedSettings.apiKeys).toEqual(originalApiKeys);
			
			// Verify individual API key loading also works
			const loadedApiKeys = loadApiKeys();
			expect(loadedApiKeys).toEqual(originalApiKeys);
		});

		it('should handle empty keys correctly after refresh', () => {
			// Save empty keys
			saveApiKeys(DEFAULT_API_KEYS);
			
			// Simulate refresh
			const loadedSettings = loadUnifiedSettings();
			
			expect(loadedSettings.apiKeys).toEqual(DEFAULT_API_KEYS);
			expect(loadedSettings.apiKeys.openai).toBe('');
			expect(loadedSettings.apiKeys.gemini).toBe('');
		});
	});
});
