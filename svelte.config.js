import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte'; // Or @sveltejs/kit/vite starting SvelteKit 2

/** @type {import('@sveltejs/kit').Config} */
const config = {
	preprocess: vitePreprocess(),
	kit: {
		adapter: adapter({
			pages: 'build',
			assets: 'build',
			fallback: 'index.html', // Crucial for SPA
			precompress: false,
		}),
		// For SPA, you might not prerender all entries, or only the shell.
		// Often, you'd disable prerendering or only prerender the root.
		prerender: {
			// entries: [] // or just don't set it, and control with `export const prerender`
		}
	},
	// Ensure appDir is set if you had a custom one, but default is fine
	// appDir: 'app',
};

export default config;