import { OpenAIAdapter } from './openAIAdapter';

/**
 * Adapter for custom OpenAI-compatible endpoints.
 */
export class CustomAdapter extends OpenAIAdapter {
    /**
     * Creates an instance of CustomAdapter.
     * @param apiKey The API key for the custom provider, if any.
     * @param model The model to use for generation.
     * @param customUrl The custom URL for the provider.
     */
    constructor(apiKey: string | null, model: string, customUrl: string) {
        if (!customUrl) {
            throw new Error("Custom API URL is required for CustomAdapter.");
        }
        super(apiKey, model, customUrl);
    }

    /**
     * Overrides the API key validation to allow for custom APIs that may not require authentication.
     */
    protected validateApiKey(): void {
        if (!this.apiKey) {
            console.warn("No API key provided for custom adapter. Some APIs may require authentication.");
        }
        // Don't throw error - let the API decide if auth is required
    }
}
