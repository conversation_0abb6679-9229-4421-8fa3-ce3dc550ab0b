# Design Documentation

This document outlines the design conventions, color palette, typography, and component styling for the AI Pad application.

## Color Palette

The application uses a clean and modern color scheme to ensure readability and a pleasant user experience.

| Role              | Color          | Hex       | Usage                               |
| ----------------- | -------------- | --------- | ----------------------------------- |
| **Primary Text**  | Dark Gray      | `#333`    | Main text, headers                  |
| **Background**    | Light Gray     | `#f0f0f0` | Main background                     |
| **Notepad BG**    | Off-White      | `#fafafa` | Notepad background                  |
| **Sidebar BG**    | Gray           | `#e9e9e9` | Sidebar background                  |
| **Header BG**     | Dark Gray      | `#333`    | Header background                   |
| **Accent**        | Green          | `#4CAF50` | Logos, buttons, links               |
| **Selection**     | Light Blue     | `#b3d4fc` | Text selection                      |
| **AI Generated**  | Light Blue BG  | `#e6f7ff` | Background for AI-generated text    |
| **Success**       | Green Tones    | -         | Success messages (e.g., `#d4edda`)  |
| **Error**         | Red Tones      | -         | Error messages (e.g., `#f8d7da`)    |
| **Info**          | Blue Tones     | -         | Info messages (e.g., `#d1ecf1`)     |

## Typography

The application uses a system UI font stack for a native feel across different operating systems.

-   **Font Family:** `system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif`
-   **Base Font Size:** `16px` (configurable in settings)
-   **Line Height:** `1.6` for the notepad editor for better readability.

## Layout

The application is divided into three main sections:

1.  **Header:** A fixed header containing the logo and API key settings.
2.  **Main Content:** A flexible area that contains the notepad editor.
3.  **Sidebar:** A fixed-width sidebar for application settings.

The layout is managed using flexbox to ensure proper alignment and responsiveness.

## Component Styles

### Buttons

-   **Primary Buttons:** Green background (`#4CAF50`) with white text. Used for key actions like the "API Keys" button.
-   **Secondary Buttons:** White background with a gray border. Used for less prominent actions like the AI feature buttons.
-   **Hover State:** Buttons have a subtle background color change on hover to provide visual feedback.
-   **Disabled State:** Disabled buttons have reduced opacity and a "not-allowed" cursor.

### Inputs & Selects

-   **Background:** All text inputs, number inputs, and select dropdowns have a white background (`#fff`).
-   **Border:** A 1px solid gray border (`#ccc`) is used for all inputs.
-   **Focus State:** On focus, inputs have a blue outline (`#007bff` or `#4CAF50`) to indicate the active element.
-   **Padding:** Consistent padding of `8px` or `10px` is used for a comfortable text entry experience.

### Modals

-   **Backdrop:** A semi-transparent black backdrop (`rgba(0, 0, 0, 0.5)`) is used to focus the user's attention on the modal.
-   **Content:** The modal content has a white background, rounded corners, and a subtle box shadow.
-   **Header & Footer:** Modals have distinct header and footer sections with a light gray background (`#f8f9fa`) to separate them from the main content.

### Status Messages

-   **Positioning:** Status messages appear at the bottom-center of the screen.
-   **Styling:** Each message type (success, error, info) has a distinct background color and text color to quickly convey its meaning.

## Interactive States

-   **Hover:** Most interactive elements, including buttons and dropdown items, change background color on hover.
-   **Focus:** Input fields and other focusable elements have a visible outline when they are active.
-   **Selection:** Text selection is highlighted with a light blue background.
