import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';
import path from 'path';

export default defineConfig({
	plugins: [sveltekit()],
	test: {
		include: ['tests/**/*.{test,spec}.{js,ts}'],
		environment: 'jsdom',
		setupFiles: ['./tests/setup.ts'],
		globals: true
	},
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src/lib'),
			'$lib': path.resolve(__dirname, './src/lib')
		}
	}
});
