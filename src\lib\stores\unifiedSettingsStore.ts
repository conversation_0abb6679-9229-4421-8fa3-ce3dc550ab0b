import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import type { AppSettings, ApiKeys, CustomModelHistory, AIProviderType, ProviderSpecificModels } from '../types';
import { loadUnifiedSettings, saveUnifiedSettings } from '../services/storageService';
import { DEFAULT_APP_SETTINGS, DEFAULT_API_KEYS, DEFAULT_PROVIDER_SPECIFIC_MODELS, AVAILABLE_MODELS } from '../config';
import { isCustomModel } from '../utils/modelUtils';

// Settings state management
export const settingsLoaded = writable(false);
let isInitialized = false;
let allowAutoSave = false;

// Core settings stores
export const appSettings = writable<AppSettings>({ ...DEFAULT_APP_SETTINGS });
export const apiKeys = writable<ApiKeys>({ ...DEFAULT_API_KEYS });
export const notepadContent = writable<string>("");
export const customModelHistory = writable<CustomModelHistory>({ gemini: [], openai: [], custom: [] });
export const providerSpecificModels = writable<ProviderSpecificModels>({ ...DEFAULT_PROVIDER_SPECIFIC_MODELS });

// Combined settings store for easy access
export const unifiedSettings = derived(
    [appSettings, apiKeys, notepadContent, customModelHistory, providerSpecificModels],
    ([$appSettings, $apiKeys, $notepadContent, $customModelHistory, $providerSpecificModels]) => ({
        appSettings: $appSettings,
        apiKeys: $apiKeys,
        notepadContent: $notepadContent,
        customModelHistory: $customModelHistory,
        providerSpecificModels: $providerSpecificModels
    })
);

// Validate and fix app settings to ensure consistency
function validateAppSettings(settings: AppSettings, loaded: any): AppSettings {
    const validSettings = { ...settings };
    const provider = validSettings.aiProvider;
    const savedModel = loaded.providerSpecificModels[provider];

    if (provider === 'custom') {
        if (savedModel && isCustomModel(savedModel)) {
            validSettings.aiModel = savedModel;
        } else {
            const history = loaded.customModelHistory.custom || [];
            validSettings.aiModel = history.length > 0 && isCustomModel(history[0].model) ? history[0].model : '';
        }
    } else {
        const availableModels = AVAILABLE_MODELS[provider] || [];
        if (savedModel && availableModels.some(m => m.id === savedModel)) {
            validSettings.aiModel = savedModel;
        } else if (availableModels.length > 0) {
            validSettings.aiModel = availableModels[0].id;
        }
    }
    return validSettings;
}

// Removes predefined models from custom model history
function cleanCustomModels(history: CustomModelHistory): CustomModelHistory {
    const cleanedHistory = { ...history };
    for (const provider in cleanedHistory) {
        if (provider !== 'custom') {
            cleanedHistory[provider] = (cleanedHistory[provider] || []).filter(item => isCustomModel(item.model));
        }
    }
    return cleanedHistory;
}

// Initialize all settings from storage
export function initializeAllSettings(): Promise<void> {
    if (isInitialized) return Promise.resolve();
    isInitialized = true;

    if (!browser) {
        settingsLoaded.set(true);
        return Promise.resolve();
    }

    try {
        const loaded = loadUnifiedSettings();
        const cleanedHistory = cleanCustomModels(loaded.customModelHistory);
        
        providerSpecificModels.set(loaded.providerSpecificModels);
        
        const validatedSettings = validateAppSettings(loaded.appSettings, loaded);

        appSettings.set(validatedSettings);
        apiKeys.set(loaded.apiKeys);
        notepadContent.set(loaded.notepadContent);
        customModelHistory.set(cleanedHistory);

        settingsLoaded.set(true);
        setTimeout(setupAutoSave, 50);
    } catch (error) {
        console.error('Failed to initialize settings:', error);
        settingsLoaded.set(true);
    }
    return Promise.resolve();
}

// Auto-save settings when any store changes
function autoSaveSettings() {
    if (!browser || !allowAutoSave || !get(settingsLoaded)) return;
    saveUnifiedSettings(get(unifiedSettings));
}

// Enable auto-save for all stores
export function setupAutoSave() {
    if (!browser || allowAutoSave) return;
    allowAutoSave = true;
    const stores = [appSettings, apiKeys, notepadContent, customModelHistory, providerSpecificModels];
    stores.forEach(store => store.subscribe(autoSaveSettings));
}

// Update a specific app setting
export function updateAppSetting<K extends keyof AppSettings>(key: K, value: AppSettings[K]) {
    appSettings.update(settings => ({ ...settings, [key]: value }));
}

// Update the model selection and save it
export function updateModelSelection(newModel: string) {
    const currentProvider = get(appSettings).aiProvider;
    appSettings.update(settings => ({ ...settings, aiModel: newModel }));
    providerSpecificModels.update(models => ({ ...models, [currentProvider]: newModel }));
}

// Switch AI provider and restore appropriate model
export function switchAIProvider(newProvider: AIProviderType) {
    const currentSettings = get(appSettings);
    if (currentSettings.aiProvider === newProvider) return;

    // Save current model before switching
    updateModelSelection(currentSettings.aiModel);

    // Restore model for new provider
    const newModel = get(providerSpecificModels)[newProvider] || 
                     (newProvider !== 'custom' && AVAILABLE_MODELS[newProvider]?.length > 0 ? AVAILABLE_MODELS[newProvider][0].id : '');
    
    appSettings.update(settings => ({ ...settings, aiProvider: newProvider, aiModel: newModel }));
}

// Update a specific API key
export function updateApiKey<K extends keyof ApiKeys>(key: K, value: ApiKeys[K]) {
    apiKeys.update(keys => ({ ...keys, [key]: value }));
}

// Update notepad content
export function updateNotepadContent(content: string) {
    notepadContent.set(content);
}

// Get available models for current provider
export const availableModels = derived(appSettings, $appSettings => {
    return $appSettings.aiProvider === 'custom' ? [] : AVAILABLE_MODELS[$appSettings.aiProvider] || [];
});

// Get description of currently selected model
export const currentModelDescription = derived([appSettings, availableModels], ([$appSettings, $availableModels]) => {
    if ($appSettings.aiProvider === 'custom') return null;
    return $availableModels.find(m => m.id === $appSettings.aiModel)?.name || null;
});

// Add custom model to history (max 10 items)
export function addModelToHistory(provider: AIProviderType, model: string) {
    if (!model || !isCustomModel(model)) return;

    customModelHistory.update(history => {
        const providerHistory = [...(history[provider] || [])];
        const existingIndex = providerHistory.findIndex(item => item.model === model);
        if (existingIndex >= 0) {
            providerHistory.splice(existingIndex, 1);
        }
        providerHistory.unshift({ model, lastUsed: Date.now() });
        return { ...history, [provider]: providerHistory.slice(0, 10) };
    });
}

// Remove model from history
export function removeModelFromHistory(provider: AIProviderType, model: string) {
    customModelHistory.update(history => ({
        ...history,
        [provider]: (history[provider] || []).filter(item => item.model !== model)
    }));
}
