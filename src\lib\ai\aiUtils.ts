import { API_TIMEOUT } from '../config';

/**
 * Fetches a resource with a specified timeout.
 * @param resource The resource to fetch.
 * @param options The options for the fetch request.
 * @param timeout The timeout in milliseconds.
 * @returns A promise that resolves to the response.
 * @throws Will throw an error if the fetch request times out.
 */
export async function fetchWithTimeout(
    resource: RequestInfo | URL,
    options: RequestInit = {},
    timeout: number = API_TIMEOUT
): Promise<Response> {
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(resource, {
        ...options,
        signal: controller.signal
    });
    clearTimeout(id);
    return response;
}
