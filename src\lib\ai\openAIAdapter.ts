import { AIProviderAdapter } from './aiProvider';
import { OPENROUTER_HEADERS } from '../config';

/**
 * Adapter for the OpenAI API and compatible endpoints.
 */
export class OpenAIAdapter extends AIProviderAdapter {
    /**
     * Generates a response from the OpenAI API.
     * @param prompt The prompt to send to the AI.
     * @returns A promise that resolves to the generated text.
     */
    async generate(prompt: string): Promise<string> {
        this.validateApiKey();

        const url = this.buildUrl();
        const headers = this.buildHeaders();
        const body = {
            model: this.model,
            messages: [{ role: "user", content: prompt }]
        };

        const data = await this.makeRequest(url, body, headers);

        // Handle OpenAI response structure
        if (data.choices?.[0]?.message?.content) {
            return data.choices[0].message.content;
        }

        throw new Error("Unexpected OpenAI API response structure.");
    }

    /**
     * Builds the appropriate URL for OpenAI or custom endpoints.
     * @returns The URL for the API request.
     */
    private buildUrl(): string {
        if (this.customUrl) {
            return this.customUrl.endsWith('/chat/completions')
                ? this.customUrl
                : `${this.customUrl.replace(/\/$/, '')}/chat/completions`;
        }
        return 'https://api.openai.com/v1/chat/completions';
    }

    /**
     * Builds the headers for the API request.
     * @returns The headers for the API request.
     */
    private buildHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'Authorization': `Bearer ${this.apiKey}`
        };

        // Add OpenRouter-specific headers if needed
        if (this.customUrl?.includes('openrouter.ai')) {
            return { ...headers, ...OPENROUTER_HEADERS };
        }

        return headers;
    }

}
