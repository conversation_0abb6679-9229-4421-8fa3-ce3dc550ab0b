import type { AIProvider as AIProviderInterface, ApiKeys, AppSettings, AIProviderType } from '../types';
import { GeminiAdapter } from '../ai/geminiAdapter';
import { OpenAIAdapter } from '../ai/openAIAdapter';
import { CustomAdapter } from '../ai/customAdapter';
import { showStatus } from '../stores/statusStore';
import { AVAILABLE_MODELS } from '../config';
import { processThinkingResponse, type ProcessedResponse } from '../utils/thinkingUtils';
import { get } from 'svelte/store';
import { apiKeys as apiKeysStore, appSettings as appSettingsStore } from '../stores/unifiedSettingsStore';


/**
 * Validates the model selection for the current provider.
 * @param provider The AI provider.
 * @param modelId The selected model ID.
 * @returns The validated model ID.
 */
function validateModel(provider: AIProviderType, modelId: string): string {
    if (provider === 'custom') {
        if (!modelId?.trim()) {
            throw new Error("Custom model name cannot be empty.");
        }
        return modelId.trim();
    }

    const availableModels = AVAILABLE_MODELS[provider] || [];
    if (!availableModels.some(m => m.id === modelId) && availableModels.length > 0) {
        console.warn(`Model '${modelId}' not found for ${provider}. Using default.`);
        return availableModels[0].id;
    }

    if (availableModels.length === 0) {
        throw new Error(`No models configured for provider: ${provider}`);
    }

    return modelId;
}

/**
 * Creates an AI adapter based on the specified provider.
 * @param provider The AI provider.
 * @param apiKeys The API keys.
 * @param model The selected model.
 * @returns An instance of an AI provider adapter.
 */
function createAdapter(provider: AIProviderType, apiKeys: ApiKeys, model: string): AIProviderInterface {
    switch (provider) {
        case 'gemini':
            if (!apiKeys.gemini) throw new Error("Gemini API Key is missing.");
            return new GeminiAdapter(apiKeys.gemini, model);

        case 'openai':
            if (!apiKeys.openai) throw new Error("OpenAI API Key is missing.");
            return new OpenAIAdapter(apiKeys.openai, model);

        case 'custom':
            if (!apiKeys.customUrl) throw new Error("Custom API URL is missing.");
            return new CustomAdapter(apiKeys.customKey, model, apiKeys.customUrl);

        default:
            throw new Error(`Unsupported AI provider: ${provider}`);
    }
}

/**
 * Calls the AI with thinking support.
 * @param prompt The prompt to send to the AI.
 * @returns A promise that resolves to the processed AI response, or null if an error occurs.
 */
export async function callAIWithThinking(prompt: string): Promise<ProcessedResponse | null> {
    try {
        const apiKeys = get(apiKeysStore);
        const settings = get(appSettingsStore);

        // Validate and get effective model
        const effectiveModel = validateModel(settings.aiProvider, settings.aiModel);

        // Create adapter
        const adapter = createAdapter(settings.aiProvider, apiKeys, effectiveModel);

        showStatus(`Calling AI (${settings.aiProvider}, ${effectiveModel})...`, 'info');

        // Make the API call
        const rawResult = await adapter.generate(prompt);
        showStatus("AI response received.", "success");

        // Process and return response
        return processThinkingResponse(rawResult);

    } catch (error: any) {
        console.error("AI Call Error:", error);
        showStatus(`AI Error: ${error.message}`, "error");
        return null;
    }
}
