# Stores Documentation

## Overview

The application uses Svelte stores for state management with automatic persistence and reactivity. All stores have been simplified for better maintainability.

## Unified Settings Store (`unifiedSettingsStore.ts`)

### Purpose
Central store managing all application settings with automatic persistence and validation.

### Core Stores

#### `appSettings: Writable<AppSettings>`
Application configuration including:
- `fontFamily: string` - CSS font family
- `fontSize: string` - Font size in pixels
- `aiProvider: AIProviderType` - Current AI provider
- `aiModel: string` - Selected model ID
- `autocompleteContextLength: number` - Context characters for AI

#### `apiKeys: Writable<ApiKeys>`
API credentials for all providers:
- `gemini: string` - Google Gemini API key
- `openai: string` - OpenAI API key
- `customUrl: string` - Custom provider base URL
- `customKey: string` - Custom provider API key

#### `notepadContent: Writable<string>`
Current text content of the notepad.

#### `customModelHistory: Writable<CustomModelHistory>`
History of custom models per provider:
```typescript
{
  [provider: string]: ModelHistoryItem[]
}
```

#### `providerSpecificModels: Writable<ProviderSpecificModels>`
Last selected model for each provider.

### Derived Stores

#### `unifiedSettings`
Combines all individual stores into a single reactive object for easy access and persistence.

#### `availableModels`
Returns available models for the current provider:
- Empty array for custom provider
- Predefined models for OpenAI/Gemini

#### `currentModelDescription`
Returns human-readable description of the currently selected model.

### State Management

#### `settingsLoaded: Writable<boolean>`
Indicates whether settings have been loaded from storage.

### Core Functions

#### `initializeAllSettings(): Promise<void>`
- Loads settings from storage
- Validates and fixes inconsistencies
- Restores model selections per provider
- Cleans contaminated custom model data
- Sets up auto-save after initialization

#### `setupAutoSave()`
- Enables automatic saving on any store change
- Subscribes to all individual stores
- Only activates after initialization

#### `validateAppSettings(settings, loaded): AppSettings`
- Ensures the selected model is valid for the current provider.
- Falls back to the first available model if the saved model is invalid.

#### `cleanCustomModels(history): CustomModelHistory`
- Removes any predefined models that may have been saved to the custom model history.

### Helper Functions

#### `updateAppSetting<K>(key: K, value: AppSettings[K])`
- Updates a specific app setting.

#### `updateApiKey<K>(key: K, value: ApiKeys[K])`
- Updates a specific API key.

#### `updateNotepadContent(content: string)`
- Updates the notepad content.

#### `switchAIProvider(newProvider: AIProviderType)`
- Saves current model selection
- Switches to new provider
- Restores appropriate model for new provider
- Handles both predefined and custom models

#### `addModelToHistory(provider: AIProviderType, model: string)`
- Adds custom model to history
- Moves to front if already exists
- Limits to 10 most recent models
- Only accepts custom models

#### `removeModelFromHistory(provider: AIProviderType, model: string)`
- Removes model from history
- Updates store reactively

### Provider Switching Logic

The store maintains the last selected model for each provider in `providerSpecificModels`. When switching providers, the store automatically restores the last used model for that provider.

### Edge Cases Handled

1. **Invalid Model Selection**
   - Falls back to first available model
   - Logs warning for debugging

2. **Provider Switch with No History**
   - Uses first available model for predefined providers
   - Empty string for custom provider

3. **Contaminated Custom Models**
   - Removes predefined models from custom storage
   - Prevents confusion between provider types

4. **Storage Corruption**
   - Falls back to defaults
   - Continues operation gracefully

5. **Multiple Initialization**
   - Prevents duplicate initialization
   - Returns early if already initialized

---

## Status Store (`statusStore.ts`)

### Purpose
Manages user feedback messages and notifications.

### Store
#### `statusMessages: Writable<StatusMessage[]>`
- An array of `StatusMessage` objects.

### Functions
#### `showStatus(text: string, type: StatusType, duration?: number)`
- Displays a status message to the user.
- The message is automatically removed after the specified duration.

#### `clearStatus(id: number)`
- Removes a specific status message by its ID.

### Message Types
- `info` - General information
- `success` - Successful operations
- `error` - Error conditions
